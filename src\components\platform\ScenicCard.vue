<template>
  <view class="scenic-card" @click="handleCardClick">
    <view class="card-image-wrapper">
      <image class="card-image" :src="spot.image" mode="aspectFill"></image>
      <!-- 渐变遮罩 -->
      <view class="image-overlay"></view>
    </view>
    
    <view class="card-content">
      <view class="card-header">
        <text class="card-title">{{ spot.name }}</text>
        <view class="card-action-btn">
          <u-icon name="camera" size="12" color="#6D69CD"></u-icon>
          <text class="action-text">打卡</text>
        </view>
      </view>

      <view class="card-footer">
        <view class="distance-info">
          <text class="card-distance">距您约{{ spot.distance }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props
const props = defineProps({
  spot: {
    type: Object,
    required: true,
    default: () => ({
      id: 0,
      name: '',
      distance: '',
      image: ''
    })
  }
})

// 定义emits
const emit = defineEmits(['click'])

// 处理卡片点击
const handleCardClick = () => {
  emit('click', props.spot)
}
</script>

<style lang="scss" scoped>
.scenic-card {
  width: 100%;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 0;

  &:active {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .card-image-wrapper {
    position: relative;
    height: 140px;
    overflow: hidden;

    .card-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    /* 图片悬停效果 */
    &:active .card-image {
      transform: scale(1.05);
    }

    /* 渐变遮罩 */
    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
      pointer-events: none;
    }

    .card-badge {
      position: absolute;
      bottom: 12px;
      right: 12px;
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #9AFF02 0%, #C8FF02 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.9);

      .badge-text {
        font-size: 12px;
        font-weight: 700;
        color: #333;
      }
    }
  }

  .card-content {
    padding: 14px 16px 16px 16px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      min-height: 20px;

      .card-title {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        line-height: 1.3;
        flex: 1;
        margin-right: 8px;
        /* 文字超出省略 - 支持多行省略 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        /* 设置最大高度 */
        max-height: 2.6em; /* 2行的高度 */
        /* 设置最大宽度确保省略号生效 */
        max-width: 100%;
        word-break: break-word;
      }

      .card-action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 5px 10px;
        border-radius: 20px;
        border: 1px solid #6D69CD;
        transition: all 0.3s ease;
        flex-shrink: 0;
        height: 24px;

        &:active {
          background: linear-gradient(135deg, rgba(154, 255, 2, 0.2) 0%, rgba(200, 255, 2, 0.25) 100%);
          transform: scale(0.95);
          border-color: #6D69CD;
          
        }

        .action-text {
          font-size: 11px;
          color: #6D69CD;
          font-weight: 600;
          line-height: 1;
        }
      }
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .distance-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .card-distance {
          font-size: 11px;
          color: #999;
          font-weight: 400;
        }
      }

      .rating-info {
        display: flex;
        align-items: center;
        gap: 3px;

        .rating-text {
          font-size: 11px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
