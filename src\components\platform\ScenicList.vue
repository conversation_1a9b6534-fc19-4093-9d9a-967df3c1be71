<template>
  <view class="scenic-list">
    <view class="waterfall-column" v-for="(column, columnIndex) in waterfallColumns" :key="columnIndex">
      <ScenicCard
        v-for="spot in column"
        :key="spot.id"
        :spot="spot"
        @click="handleSpotClick"
      />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import ScenicCard from './ScenicCard.vue'

// 定义props
const props = defineProps({
  scenicSpots: {
    type: Array,
    required: true,
    default: () => []
  },
  columns: {
    type: Number,
    default: 2
  }
})

// 定义emits
const emit = defineEmits(['spotClick'])

// 计算瀑布流列布局 - 直接处理一维数组
const waterfallColumns = computed(() => {
  const columnCount = props.columns
  const columns = Array.from({ length: columnCount }, () => [])

  // 直接使用一维数组
  props.scenicSpots.forEach((spot, index) => {
    const columnIndex = index % columnCount
    columns[columnIndex].push(spot)
  })

  return columns
})

// 处理景区卡片点击
const handleSpotClick = (spot) => {
  emit('spotClick', spot)
}
</script>

<style lang="scss" scoped>
.scenic-list {
  padding: 0;
  margin-bottom: 0;
  display: flex;
  gap: 12px;
  align-items: flex-start;

  .waterfall-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}
</style>
