import request from '@/utils/request'
import config from '@/config'
import { getToken } from '@/utils/auth'

/**
 * 根据模版合成视频
 * @param data
 * @returns
 */
export function synthesizeVideo(data) {

  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${config.baseUrl}/media/mediaProducing/submit`,
      files: data.files,
      formData: {
        templateId: data.templateId,
        clipsParam: data.clipsParam,
        projectMetadata: data.projectMetadata
      },
      header: {
        'Authorization': `Bearer ${getToken()}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const parsedData = JSON.parse(res.data);
          resolve(parsedData);
        }
      },
      fail: (err) => {
        console.error("addAudio上传失败>>>", err);
        reject({
          code: -1,
          msg: "网络请求失败",
          error: err
        });
      },
    })
  });

}

/**
 * 获取剪辑工程列表
 */
export function listEditingProjects(params) {
  return request({
    url: '/video/project/listEditingProjects',
    method: 'GET',
    params: {
      keyword: params.projectId,
      status: params.status,
      startTime: params.startTime,
      endTime: params.endTime,
      nextToken: params.nextToken,
      maxResults: params.maxResults || 10
    }
  });
}

/**
 * 通过数据库------获取剪辑工程列表
 */
export function listEditingProjectsDB(params) {
  return request({
    url: '/video/project/listEditingProjectsDB',
    method: 'GET',
    params: {
      keyword: params.projectId,
      status: params.status,
      startTime: params.startTime,
      endTime: params.endTime,
    }
  });
}


/**
 * 删除剪辑工程
 */
export function deleteEditingProjects(projectIds) {
  return request({
    url: '/video/project/deleteEditingProjects',
    method: 'POST',
    params: {
      projectIds: projectIds.join(',')
    }
  });
}
