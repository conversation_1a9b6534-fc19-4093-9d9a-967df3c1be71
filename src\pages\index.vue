<template>
  <view class="container">
    <!-- 状态栏占位 -->
    <view class="status-bar-placeholder"></view>

    <!-- 现代化标题栏 -->
    <view class="modern-header">
      <view class="header-content">
        <!-- 品牌区域 -->
        <view class="brand-section">
          <view class="brand-info">
            <text class="app-name">云剪辑</text>
            <text class="app-subtitle">看见美好 拍出精彩</text>
          </view>
        </view>

        <!-- 操作区域 -->
        <view class="header-actions">
          <view class="location-chip" @click="handleLocationClick">
            <text class="location-text">山东省</text>
            <u-icon name="arrow-down" size="10" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-container">
        <u-search
          placeholder="搜索景区"
          :show-action="false"
          shape="round"
          color="#333"
          borderColor="#e5e5e5"
          height="80rpx"
          :clearabled="true"
          @search="handleSearch"
        ></u-search>
      </view>
    </view>

    <!-- 功能区域 - 分离式设计 -->
    <view class="feature-section">
      <up-box height="370rpx" gap="20rpx" borderRadius="40rpx">
        <template #left>
          <view
            class="feature-item main-feature"
            @click="handleIconClick(functionIcons[0])"
          >
            <view class="feature-icon-wrapper">
              <view
                class="feature-icon"
                :style="{ background: functionIcons[0].gradient }"
              >
                <u-icon
                  :name="functionIcons[0].icon"
                  size="56"
                  color="#fff"
                ></u-icon>
              </view>
            </view>
            <view class="feature-content">
              <text class="feature-title">{{ functionIcons[0].text }}</text>
              <text class="feature-subtitle">探索精彩世界</text>
            </view>
          </view>
        </template>
        <template #rightTop>
          <view
            class="feature-item sub-feature"
            @click="handleIconClick(functionIcons[1])"
          >
            <view
              class="feature-icon"
              :style="{ background: functionIcons[1].gradient }"
            >
              <u-icon
                :name="functionIcons[1].icon"
                size="40"
                color="#fff"
              ></u-icon>
            </view>
            <text class="feature-title">{{ functionIcons[1].text }}</text>
          </view>
        </template>
        <template #rightBottom>
          <view
            class="feature-item sub-feature"
            @click="handleIconClick(functionIcons[2])"
          >
            <view
              class="feature-icon"
              :style="{ background: functionIcons[2].gradient }"
            >
              <u-icon
                :name="functionIcons[2].icon"
                size="40"
                color="#fff"
              ></u-icon>
            </view>
            <text class="feature-title">{{ functionIcons[2].text }}</text>
          </view>
        </template>
      </up-box>
    </view>

    <view class="white-content-area">
      <!-- 景区推荐标题 -->
      <view class="section-title">
        <text class="title-text">为你匹配了最近的景区</text>
        <view class="title-right">
          <text class="more-text">去看看附近更多推荐</text>
        </view>
      </view>

      <!-- 景区卡片列表 -->
      <ScenicList :scenic-spots="scenicSpots" @spot-click="handleSpotClick" />
    </view>
  </view>
</template>

<script setup>
import { reactive } from "vue";
import ScenicList from "@/components/platform/ScenicList.vue";

// 功能图标数据
const functionIcons = reactive([
  {
    icon: "map",
    text: "景区",
    gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  },
  {
    icon: "play-circle",
    text: "视频",
    gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
  },
  {
    icon: "car",
    text: "交通",
    gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
  },
]);

// 景区数据
const scenicSpots = reactive([
  {
    id: 1,
    name: "中国孙子文化园",
    distance: "126.7km",
    image:
      "https://x0.ifengimg.com/ucms/2019_52/5C6AAB16819F6B3C4A46FD82AC9E6DDC8072D4D6_w1080_h588.jpg",
  },
  {
    id: 2,
    name: "夫子山风景区",
    distance: "282.1km",
    image: "https://youimg1.c-ctrip.com/target/100e1600000101s6mC4D4.jpg",
  },
  {
    id: 5,
    name: "泰山风景名胜区",
    distance: "89.3km",
    image:
      "https://x0.ifengimg.com/ucms/2021_23/AD89AC34FC60D63C2CCABB61B13EFE69769509F6_size76_w1080_h659.jpg",
  },
  {
    id: 6,
    name: "青岛栈桥",
    distance: "156.8km",
    image: "https://pic4.zhimg.com/v2-03e1be449411d60698e7fcacfa79ac73_b.jpg",
  },
  {
    id: 7,
    name: "济南大明湖",
    distance: "45.2km",
    image: "https://pic.nximg.cn/file/20221017/31372278_100755222106_2.jpg",
  },
  {
    id: 3,
    name: "卡丁车场",
    distance: "126.7km",
    image: "https://pic4.zhimg.com/v2-a520ada2f17367d14ea48e3ad379f843_r.jpg",
  },
  {
    id: 4,
    name: "洛阳西泰山",
    distance: "538.4km",
    image: "https://youimg1.c-ctrip.com/target/100614000000w87ptD211.jpg",
  },
  {
    id: 8,
    name: "威海刘公岛",
    distance: "234.5km",
    image: "https://youimg1.c-ctrip.com/target/100e1600000101s6mC4D4.jpg",
  },
  {
    id: 9,
    name: "烟台蓬莱阁",
    distance: "198.7km",
    image:
      "https://imgbdb3.bendibao.com/ytbdb/tour/20212/24/2021224103155_17846.jpg",
  },
]);

// 处理功能图标点击
const handleIconClick = (item) => {
  console.log("点击功能图标:", item.text);
  // 这里可以添加路由跳转逻辑
};

// 处理景区卡片点击
const handleSpotClick = (spot) => {
  console.log("点击景区:", spot.name);
  // 跳转到景区详情页
  uni.navigateTo({
    url: `/pages_homepage/pages/scenicDetail/index?id=${
      spot.id
    }&name=${encodeURIComponent(spot.name)}`,
  });
};

// 处理定位点击
const handleLocationClick = () => {
  console.log("点击定位选择");
  // 这里可以添加城市选择功能
};

// 处理搜索输入
const handleInput = (value) => {
  console.log("搜索输入:", value);
  // 这里可以添加实时搜索逻辑
};

// 处理搜索提交
const handleSearch = (value) => {
  console.log("搜索提交:", value);
  // 这里可以添加搜索结果页面跳转或搜索逻辑
};

// 处理通知点击
const handleNotificationClick = () => {
  console.log("点击通知");
  // 这里可以添加通知页面跳转
};

// 处理搜索过滤器点击
const handleFilterClick = () => {
  console.log("点击搜索过滤器");
  // 这里可以添加过滤选项弹窗
};
</script>

<style lang="scss" scoped>
/* 全局隐藏滚动条 */
::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 兼容Firefox */
* {
  scrollbar-width: none !important;
}

/* 隐藏所有可能的滚动条 */
html::-webkit-scrollbar,
body::-webkit-scrollbar,
page::-webkit-scrollbar,
view::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 确保没有滚动条轨道 */
::-webkit-scrollbar-track {
  display: none !important;
}

/* 确保没有滚动条滑块 */
::-webkit-scrollbar-thumb {
  display: none !important;
}

.container {
  min-height: 100vh;
  background: #ffffff;
  position: relative;
  /* 确保没有滚动条 */
  overflow-x: hidden;
}

/* 处理状态栏区域和隐藏滚动条 */
page {
  background: #ffffff;
  /* 隐藏页面滚动条 */
  overflow-x: hidden;
}

/* 全局隐藏滚动条 */
page::-webkit-scrollbar {
  display: none;
}

/* 隐藏body滚动条 */
body::-webkit-scrollbar {
  display: none;
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: var(--status-bar-height);
  background: transparent;
}

/* 现代化标题栏 */
.modern-header {
  padding: 40rpx 10rpx 20rpx 10rpx;
  position: relative;
  background: #ffffff;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .brand-section {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .brand-logo {
        width: 88rpx;
        height: 88rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
      }

      .brand-info {
        .app-name {
          font-size: 48rpx;
          font-weight: 700;
          color: #1a1a1a;
          display: block;
          letter-spacing: -1rpx;
          line-height: 1.2;
        }

        .app-subtitle {
          font-size: 24rpx;
          color: #666;
          margin-top: 4rpx;
          display: block;
          font-weight: 400;
          opacity: 0.8;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .location-chip {
        display: flex;
        align-items: center;
        gap: 8rpx;
        background: rgba(255, 255, 255, 0.9);
        padding: 16rpx 24rpx;
        border-radius: 40rpx;
        backdrop-filter: blur(20rpx);
        border: 2rpx solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

        &:active {
          background: rgba(255, 255, 255, 1);
          transform: scale(0.96);
        }

        .location-text {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }
      }

      .notification-btn {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(20rpx);
        border: 2rpx solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

        &:active {
          background: rgba(255, 255, 255, 1);
          transform: scale(0.95);
        }

        .notification-dot {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          width: 16rpx;
          height: 16rpx;
          background: #ff4757;
          border-radius: 50%;
          border: 4rpx solid #fff;
        }
      }
    }
  }
}

/* 功能区域 - 简洁美观设计 */
.feature-section {
  padding: 20rpx 10rpx 30rpx 10rpx;
  position: relative;
  background: #ffffff;

  .feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20rpx;
    border-radius: 32rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;

    &:active {
      transform: scale(0.98);
    }

    .feature-icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .feature-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #333;
      text-align: center;
      line-height: 1.2;
    }
  }

  /* 主功能卡片 (左侧) */
  .main-feature {
    flex-direction: column;
    gap: 20rpx;
    padding: 40rpx 32rpx;

    .feature-icon-wrapper {
      position: relative;
    }

    .feature-icon {
      width: 112rpx;
      height: 112rpx;
      border-radius: 36rpx;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.2);
    }

    .feature-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
    }

    .feature-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a1a1a;
    }

    .feature-subtitle {
      font-size: 22rpx;
      font-weight: 500;
      color: #666;
      opacity: 0.8;
    }
  }

  /* 次要功能卡片 (右侧) */
  .sub-feature {
    flex-direction: column;
    gap: 12rpx;
    padding: 24rpx 20rpx;

    .feature-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 20rpx;
    }

    .feature-title {
      font-size: 22rpx;
      font-weight: 600;
    }
  }
}

/* 简洁过渡区域 */
.elegant-transition {
  height: 40px;
  background: transparent;
  position: relative;
}

/* 白色内容区域 */
.white-content-area {
  background: #ffffff;
  margin: 0 -20rpx;
  padding: 0 40rpx 0 40rpx;
  min-height: 50vh;
  /* 确保底部没有额外的边框或阴影 */
  border: none;
  box-shadow: none;
  /* 添加底部安全区域内边距 */
  padding-bottom: calc(60rpx + env(safe-area-inset-bottom));
  position: relative;
}

/* 景区推荐标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 64rpx 0 48rpx 0;
  margin-bottom: 16rpx;
  position: relative;

  /* 现代化装饰线 */
  &::after {
    content: "";
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 50px;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: slideIn 0.8s ease-out;
  }

  .title-text {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -1rpx;
    line-height: 1.3;
  }

  .title-right {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 32rpx;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.1) 0%,
      rgba(118, 75, 162, 0.1) 100%
    );
    border-radius: 48rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20rpx);

    &:active {
      background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 0.2) 0%,
        rgba(118, 75, 162, 0.2) 100%
      );
      transform: scale(0.96);
      border-color: rgba(102, 126, 234, 0.3);
    }

    .more-text {
      font-size: 24rpx;
      color: #667eea;
      font-weight: 600;
    }
  }
}

/* 动画效果 */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes slideIn {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 50px;
    opacity: 1;
  }
}

@keyframes searchGlow {
  0%,
  100% {
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15),
      0 4rpx 16rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(102, 126, 234, 0.1);
  }
  50% {
    box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.25),
      0 6rpx 20rpx rgba(0, 0, 0, 0.12), 0 3rpx 12rpx rgba(102, 126, 234, 0.2);
  }
}



</style>
