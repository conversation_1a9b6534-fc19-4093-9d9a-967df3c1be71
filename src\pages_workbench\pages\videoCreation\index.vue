<template>
  <view class="video-creation">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <view class="back-arrow"></view>
      </view>
      <text class="header-title">视频创作</text>
      <view class="header-placeholder"></view>
    </view>

    <!-- 模版信息展示 -->
    <view class="template-info">
      <view class="template-preview">
        <image
          :src="templateInfo.preview"
          mode="aspectFill"
          class="template-image"
        ></image>
        <view class="template-overlay">
          <view class="play-icon">
            <text class="play-symbol">▶</text>
          </view>
        </view>
      </view>
      <view class="template-details">
        <text class="template-name">{{ templateInfo.name }}</text>
        <text class="template-desc">{{ templateInfo.description }}</text>
        <view class="template-tags">
          <text class="tag">{{ templateInfo.duration }}</text>
          <text class="tag">{{ templateInfo.style }}</text>
          <text
            class="tag video-count-tag"
            v-if="templateInfo.requiredVideoCount > 0"
          >
            需要{{ templateInfo.requiredVideoCount }}个视频
          </text>
          <text class="tag video-count-tag" v-else> 无需上传视频 </text>
        </view>
      </view>
    </view>

    <!-- 任务信息表单 -->
    <view class="form-section">
      <view class="form-card">
        <view class="form-header">
          <text class="form-title">任务信息</text>
        </view>

        <view class="form-item">
          <text class="form-label">任务名称</text>
          <input
            class="form-input"
            v-model="taskInfo.name"
            placeholder="请输入任务名称"
            maxlength="50"
          />
        </view>

        <view class="form-item">
          <text class="form-label">任务描述</text>
          <textarea
            class="form-textarea"
            v-model="taskInfo.description"
            placeholder="请描述您想要创作的视频内容..."
            maxlength="200"
          />
        </view>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view class="upload-section">
      <VideoUploadCard
        v-model="videoList"
        :max-count="templateInfo.requiredVideoCount"
        :has-template="!!templateInfo.id"
        :template-name="templateInfo.name || ''"
        :disabled="isUploadDisabled"
        :is-uploading="isUploading"
        :upload-progress="uploadProgress"
        @after-read="afterRead"
        @delete="deletePic"
        @upload="handleUploadTrigger"
        @preview="handleVideoPreview"
      />
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="create-btn" :disabled="!canCreate" @click="startCreation">
        <text class="btn-text">合成</text>
      </button>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      :showIcon="true"
      iconColor="#ff4757"
      :showFileInfo="true"
      :fullFileName="deleteFileFullName"
      :displayFileName="deleteFileDisplayName"
      color="#ff4757"
      @confirm="handleConfirmDelete"
      @cancel="handleCancelDelete"
    />
  </view>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import VideoUploadCard from "@/components/platform/video-upload-card.vue";
import ConfirmDialog from "@/components/platform/confirm-dialog.vue";
import { onLoad } from "@dcloudio/uni-app";
import modal from "@/plugins/modal";
import { synthesizeVideo } from "@/api/platform/videoEdit";

// 模版信息
const templateInfo = reactive({
  templateId: "",
  name: "默认名称",
  description: "默认描述",
  previewUrl: "",
  duration: "15s",
  requiredVideoCount: 0,
  style: "vlog",
  clipsParam: {}, // 添加clipsParam字段
});

// 景区信息
const scenicInfo = reactive({
  id: 0,
  name: "默认名称",
});

// 任务信息
const taskInfo = reactive({
  name: "",
  description: "",
});

// 视频列表
const videoList = ref([]);

// 上传相关状态
const isUploading = ref(false);
const uploadProgress = ref(0);

// 删除确认对话框相关状态
const showDeleteDialog = ref(false);
const deleteFileFullName = ref("");
const deleteFileDisplayName = ref("");
const pendingDeleteFile = ref(null);
const pendingDeleteIndex = ref(-1);

// 页面加载时获取参数
onLoad((options) => {
  console.log("接收到的页面参数:", options);

  // 基本信息
  scenicInfo.id = options.scenicId;
  scenicInfo.name = decodeURIComponent(options.scenicName);
  templateInfo.id = options.templateId;
  templateInfo.name = decodeURIComponent(options.templateName);
  templateInfo.requiredVideoCount = parseInt(options.requiredVideoCount);

  // 解析clipsParam
  if (options.clipsParam) {
    try {
      templateInfo.clipsParam = JSON.parse(decodeURIComponent(options.clipsParam));
      console.log("解析到的clipsParam:", templateInfo.clipsParam);
    } catch (e) {
      console.error('解析clipsParam失败:', e);
      templateInfo.clipsParam = {};
    }
  }

  // 根据模版ID加载模版数据
  // loadTemplateData(templateInfo.id)  这里可以获取模板的播放链接，实现模板的播放

  // 设置默认任务名称（景区 + 模板名称）
  taskInfo.name = `${scenicInfo.name} - ${templateInfo.name}`;
});

// 计算是否可以创作
const canCreate = computed(() => {
  // 检查是否有模板
  if (!templateInfo.id) {
    return false;
  }

  const requiredVideoCount = templateInfo.requiredVideoCount;
  const currentVideoCount = videoList.value.length;

  // 检查基本信息
  if (!taskInfo.name.trim() || !taskInfo.description.trim()) {
    return false;
  }

  // 如果模板不需要视频（返回0），则不能进行创作
  if (requiredVideoCount === 0) {
    return false;
  }

  // 必须上传完全匹配数量的视频才能创作
  return currentVideoCount === requiredVideoCount;
});

// 计算是否禁用上传组件
const isUploadDisabled = computed(() => {
  return isUploading.value;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 处理文件上传后的回调
const afterRead = (event) => {
  const { file } = event;
  const fileList = Array.isArray(file) ? file : [file];

  fileList.forEach((item) => {
    const videoFile = {
      ...item,
      name: item.name || `视频_${Date.now()}`,
      size: item.size || 0,
      type: item.type || "video/mp4",
      url: item.url || item.path,
      path: item.path,
    };
    videoList.value.push(videoFile);
  });
};


// 删除已上传的视频
const deletePic = (file, index) => {
  // 保存待删除的文件信息
  pendingDeleteFile.value = file;
  pendingDeleteIndex.value = index;

  // 设置文件名信息
  const fileName = file.name || "未命名视频";
  deleteFileFullName.value = fileName;
  deleteFileDisplayName.value = formatFileName(fileName);

  // 显示确认对话框
  showDeleteDialog.value = true;
};

// 处理视频预览
const handleVideoPreview = (file, index) => {
  if (!file.url) return;
  // 这里可以添加视频预览逻辑
  console.log("预览视频:", file);
};

// 处理上传触发事件
const handleUploadTrigger = () => {
  console.log("触发上传事件");
  // 这里可以添加上传前的逻辑，比如检查权限、显示提示等
};

// 格式化文件名显示
const formatFileName = (fileName, maxLength = 20) => {
  if (!fileName || fileName.length <= maxLength) {
    return fileName;
  }
  const start = fileName.substring(0, Math.floor(maxLength * 0.6));
  const end = fileName.substring(fileName.length - Math.floor(maxLength * 0.3));
  return `${start}...${end}`;
};

// 确认删除
const handleConfirmDelete = () => {
  const file = pendingDeleteFile.value;
  const index = pendingDeleteIndex.value;

  if (!file) {
    return;
  }

  try {
    // 执行删除操作
    if (
      typeof index === "number" &&
      index >= 0 &&
      index < videoList.value.length
    ) {
      // 使用索引删除
      videoList.value.splice(index, 1);
      modal.msg("删除成功");
    } else {
      // 使用过滤删除（备用方案）
      const originalLength = videoList.value.length;
      videoList.value = videoList.value.filter((item) => {
        return !(
          (item.url && file.url && item.url === file.url) ||
          (item.path && file.path && item.path === file.path) ||
          (item.name && file.name && item.name === file.name &&
            item.size === file.size)
        );
      });

      if (videoList.value.length < originalLength) {
        modal.msg("删除成功");
      } else {
        modal.msg("删除失败：未找到匹配的文件");
      }
    }

    // 这里可以添加删除服务器文件的逻辑
    // if (file.url) {
    //   await deleteVideoFromServer(file.url);
    // }
  } catch (e) {
    modal.msg("删除失败");
  } finally {
    // 清理状态
    pendingDeleteFile.value = null;
    pendingDeleteIndex.value = -1;
    deleteFileFullName.value = "";
    deleteFileDisplayName.value = "";
  }
};

// 取消删除
const handleCancelDelete = () => {
  // 清理状态
  pendingDeleteFile.value = null;
  pendingDeleteIndex.value = -1;
  deleteFileFullName.value = "";
  deleteFileDisplayName.value = "";
};

// 表单验证
const validateForm = () => {
  if (!templateInfo.id) {
    modal.msg("请选择模板");
    return false;
  }

  const requiredVideoCount = templateInfo.requiredVideoCount;
  const currentVideoCount = videoList.value.length;

  // 如果模板不需要视频，直接通过验证
  if (requiredVideoCount === 0) {
    return false;
  }

  // 检查是否有上传视频
  if (currentVideoCount === 0) {
    modal.msg(`当前模板需要 ${requiredVideoCount} 个视频`);
    return false;
  }

  // 检查视频数量是否不足
  if (currentVideoCount < requiredVideoCount) {
    modal.msg(
      `视频数量不足，还需要上传 ${
        requiredVideoCount - currentVideoCount
      } 个视频 (当前 ${currentVideoCount}/${requiredVideoCount})`
    );
    return false;
  }

  // 检查任务信息
  if (!taskInfo.name.trim()) {
    modal.msg("请输入任务名称");
    return false;
  }

  if (!taskInfo.description.trim()) {
    modal.msg("请输入任务描述");
    return false;
  }

  return true;
};

// 合
const startCreation = async () => {
  if (!validateForm()) return;
  modal.loading("提交中...");
  try {
    const files = [];
    videoList.value.forEach((item) => {
      files.push({
        name: item.name,
        uri: item.url,
      });
    });

    //  拼装的数据是什么
    const projectMetadata = {
      Title: taskInfo.name?.trim() || "",
      Description: taskInfo.description?.trim() || "",
    };
    const params = {
      templateId: templateInfo.id,
      clipsParam: JSON.stringify(templateInfo.clipsParam),
      files: files,
      projectMetadata: JSON.stringify(projectMetadata),
    };

    console.log("提交参数:", params);
    const res = await synthesizeVideo(params);
    console.log("合成结果:", res);

    //关闭加载
    modal.closeLoading();

    // 显示成功提示并自动返回工作台
    modal.msg("提交成功");
    setTimeout(() => {
      // 返回到工作台页面
      uni.switchTab({
        url: '/pages_workbench/pages/index/index'
      });
    }, 1500);

  } catch (e) {
    modal.closeLoading();
    // 显示更友好的错误信息
    const errorMessage =
      e?.message || e?.data?.message || "合成失败，请稍后重试";
    modal.msg(errorMessage);
  }
};
</script>

<style lang="scss" scoped>
.video-creation {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 20px 20px 20px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .back-btn {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      background: #e0e0e0;
      transform: scale(0.95);
    }

    .back-arrow {
      width: 12px;
      height: 12px;
      border-left: 2px solid #333;
      border-bottom: 2px solid #333;
      transform: rotate(45deg);
      margin-left: 2px;
    }
  }

  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .header-placeholder {
    width: 40px;
  }
}

/* 模版信息展示 */
.template-info {
  display: flex;
  padding: 20px;
  background: #fff;
  margin-bottom: 12px;
  gap: 16px;

  .template-preview {
    position: relative;
    width: 100px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;

    .template-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .template-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .play-symbol {
          font-size: 10px;
          color: #6d69cd;
          margin-left: 1px;
        }
      }
    }
  }

  .template-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .template-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .template-desc {
      font-size: 14px;
      color: #666;
    }

    .template-tags {
      display: flex;
      gap: 8px;
      margin-top: 8px;

      .tag {
        padding: 4px 8px;
        background: rgba(109, 105, 205, 0.1);
        color: #6d69cd;
        font-size: 12px;
        border-radius: 12px;

        &.video-count-tag {
          background: linear-gradient(135deg, #6d69cd 0%, #5a56b8 100%);
          color: white;
          font-weight: 600;
        }
      }
    }
  }
}

/* 表单区域 */
.form-section {
  padding: 0 20px;
  margin-bottom: 12px;

  .form-card {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .form-header {
      margin-bottom: 20px;

      .form-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .form-item {
      margin-bottom: 20px;

      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .form-input {
        width: 100%;
        height: 48px;
        padding: 0 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        background: #fff;
        box-sizing: border-box;

        &:focus {
          border-color: #6d69cd;
          outline: none;
        }
      }

      .form-textarea {
        width: 100%;
        min-height: 80px;
        padding: 12px 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        background: #fff;
        resize: none;
        box-sizing: border-box;

        &:focus {
          border-color: #6d69cd;
          outline: none;
        }
      }
    }
  }
}

/* 上传区域 */
.upload-section {
  padding: 0 20px;
  margin-bottom: 20px;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .create-btn {
    width: 100%;
    height: 52px;
    background: linear-gradient(135deg, #9b97e8 0%, #8a85d9 100%);
    border: none;
    border-radius: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:disabled {
      background: #e0e0e0;
      cursor: not-allowed;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
    }

    .btn-text {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }
}

/* 底部安全区域 */
.safe-area {
  height: calc(92px + env(safe-area-inset-bottom));
  background: #f8f9fa;
}
</style>
